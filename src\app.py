from flask import Flask, request, jsonify
import requests
import os
from openai import OpenAI
import google.generativeai as genai
import json
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# Load environment variables
WHATSAPP_TOKEN = os.getenv("WHATSAPP_TOKEN")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
PHONE_NUMBER_ID = os.getenv("PHONE_NUMBER_ID")
WHATSAPP_VERIFY_TOKEN = os.getenv("WHATSAPP_VERIFY_TOKEN", "sasthra_whatsapp_verify_token_2025")

# Configure APIs
genai.configure(api_key=GEMINI_API_KEY)
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# WhatsApp API base URL
WHATSAPP_API = "https://graph.facebook.com/v20.0"

def send_whatsapp_message(recipient, message):
    url = f"{WHATSAPP_API}/{PHONE_NUMBER_ID}/messages"
    headers = {
        "Authorization": WHATSAPP_TOKEN,
        "Content-Type": "application/json"
    }
    data = {
        "messaging_product": "whatsapp",
        "to": recipient,
        "type": "text",
        "text": {"body": message}
    }
    response = requests.post(url, headers=headers, json=data)
    return response.status_code == 200

def get_media_url(media_id):
    url = f"{WHATSAPP_API}/{media_id}"
    headers = {"Authorization": WHATSAPP_TOKEN}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json().get("url")
    return None

def download_media(media_url):
    headers = {"Authorization": WHATSAPP_TOKEN}
    response = requests.get(media_url, headers=headers)
    if response.status_code == 200:
        return response.content
    return None

def transcribe_audio(audio_data):
    with open("temp_audio.mp3", "wb") as f:
        f.write(audio_data)
    with open("temp_audio.mp3", "rb") as f:
        transcription = openai_client.audio.transcriptions.create(
            model="whisper-1",
            file=f
        )
    os.remove("temp_audio.mp3")
    return transcription.text

def process_text_query(query):
    model = genai.GenerativeModel("gemini-1.5-flash-latest")
    prompt = f"""You are a JEE and NEET Tutor AI assistant.
- Fetch relevant information from provided documents based on the user's query and explain it with clear, complete sentences.
- Combine document content with your own knowledge for comprehensive, exam-oriented answers tailored to JEE and NEET.
- Rely on NCERT-based explanations for NEET topics and emphasize conceptual depth with examples for JEE topics.
- Responses should be between 100 and 150 tokens.
- If the query is unrelated to JEE or NEET, respond with: "I'm here to assist with JEE/NEET preparation. Please share a query related to these exams, and I'll help you!"
- Provide examples or detailed explanations when needed.
- Maintain clarity, relevance, and a supportive tone.

User question: {query}"""
    response = model.generate_content(prompt)
    return response.text

def process_image_query(image_url):
    model = genai.GenerativeModel("gemini-2.5-flash-preview-05-20")
    prompt = f"""Please provide a very brief answer (under 3000 characters) to the user's query based on the following data:
{image_url}"""
    response = model.generate_content(prompt)
    return response.text

@app.route("/webhook", methods=["POST"])
def webhook():
    data = request.get_json()
    logging.info(f"Received webhook data: {json.dumps(data, indent=2)}")

    # Verify webhook (for initial setup)
    if request.args.get("hub.verify_token") == WHATSAPP_VERIFY_TOKEN:
        return request.args.get("hub.challenge"), 200

    # Process incoming message
    if not data.get("entry", [{}])[0].get("changes"):
        return jsonify({"status": "no changes"}), 200

    changes = data["entry"][0]["changes"][0]["value"]
    messages = changes.get("messages", [])
    if not messages:
        return jsonify({"status": "no messages"}), 200

    message = messages[0]
    sender = changes["contacts"][0]["wa_id"]
    message_type = message.get("type")

    if message_type == "text":
        query = message["text"]["body"]
        response = process_text_query(query)
        send_whatsapp_message(sender, response)
    elif message_type == "audio":
        media_id = message["audio"]["id"]
        media_url = get_media_url(media_id)
        if media_url:
            audio_data = download_media(media_url)
            if audio_data:
                transcription = transcribe_audio(audio_data)
                model = genai.GenerativeModel("gemini-1.5-flash-8b-latest")
                prompt = f"""You are a JEE and NEET Tutor AI assistant.
- Fetch relevant information based on the user's query and explain it with clear, complete sentences.
- Combine document content with your own knowledge for comprehensive, exam-oriented answers tailored to JEE and NEET.
- Rely on NCERT-based explanations for NEET topics and emphasize conceptual depth with examples for JEE topics.
- Responses should be between 250 and 350 tokens.
- If the query is unrelated to JEE or NEET, respond with: "I'm here to assist with JEE/NEET preparation. Please share a query related to these exams, and I'll help you!"
- Provide examples or detailed explanations when needed.
- Maintain clarity, relevance, and a supportive tone.

User question: {transcription}"""
                response = model.generate_content(prompt)
                send_whatsapp_message(sender, response.text)
    elif message_type == "image":
        media_id = message["image"]["id"]
        media_url = get_media_url(media_id)
        if media_url:
            response = process_image_query(media_url)
            send_whatsapp_message(sender, response)
    else:
        send_whatsapp_message(sender, "I'm here to assist with JEE/NEET preparation. Please share a query related to these exams, and I'll help you!")

    return jsonify({"status": "success"}), 200

@app.route("/webhook", methods=["GET"])
def verify_webhook():
    if request.args.get("hub.verify_token") == WHATSAPP_VERIFY_TOKEN:
        return request.args.get("hub.challenge"), 200
    return "Verification failed", 403

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8035))
    app.run(host="0.0.0.0", port=port, debug=False)