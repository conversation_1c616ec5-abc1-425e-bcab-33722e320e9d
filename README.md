# WhatsApp JEE/NEET Tutor Bot

A WhatsApp bot that provides JEE and NEET tutoring assistance using OpenAI and Google Gemini APIs.

## Features

- 📱 **Text Messages**: Answers JEE/NEET related questions
- 🎵 **Audio Messages**: Transcribes audio and provides responses
- 🖼️ **Image Messages**: Analyzes images and provides explanations
- 🤖 **AI-Powered**: Uses OpenAI Whisper, GPT, and Google Gemini
- 🔒 **Secure**: Environment-based configuration
- 🐳 **Dockerized**: Easy deployment with Docker

## Quick Start

### Prerequisites

- Docker and Docker Compose
- WhatsApp Business API access
- OpenAI API key
- Google Gemini API key

### Deployment on VPS (sasthra.in)

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd Whatsup-bot
   ```

2. **Configure environment variables**:
   ```bash
   cp src/.env.production src/.env
   # Edit src/.env with your actual API keys and tokens
   nano src/.env
   ```

3. **Make deployment script executable**:
   ```bash
   chmod +x deploy.sh
   ```

4. **Deploy the application**:
   ```bash
   ./deploy.sh
   ```

5. **Configure Nginx** (if not already done):
   ```bash
   sudo cp nginx-whatsapp-bot.conf /etc/nginx/sites-available/whatsapp-bot
   sudo ln -s /etc/nginx/sites-available/whatsapp-bot /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

6. **Configure WhatsApp Webhook**:
   - Go to Facebook Developer Console
   - Navigate to WhatsApp > Configuration
   - Set webhook URL: `https://sasthra.in/webhook`
   - Set verify token: `sasthra_whatsapp_verify_token_2025`

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `WHATSAPP_TOKEN` | WhatsApp API access token | Yes |
| `PHONE_NUMBER_ID` | WhatsApp phone number ID | Yes |
| `OPENAI_API_KEY` | OpenAI API key for Whisper/GPT | Yes |
| `GEMINI_API_KEY` | Google Gemini API key | Yes |
| `WHATSAPP_VERIFY_TOKEN` | Webhook verification token | Yes |
| `PORT` | Application port (default: 5000) | No |

## API Endpoints

- `GET /webhook` - Webhook verification
- `POST /webhook` - Receive WhatsApp messages

## Docker Commands

```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Restart
docker-compose restart

# Stop
docker-compose down

# Update and redeploy
git pull && docker-compose build && docker-compose up -d
```

## Message Types Supported

1. **Text Messages**: Direct Q&A for JEE/NEET topics
2. **Audio Messages**: Transcribed using Whisper, then processed
3. **Image Messages**: Analyzed using Gemini Vision

## Security Features

- Environment-based configuration
- Non-root Docker user
- Nginx reverse proxy with SSL
- Security headers
- Request timeout protection

## Monitoring

- Health check endpoint: `https://sasthra.in/health`
- Container health checks
- Nginx access/error logs
- Application logs via Docker

## Troubleshooting

### Check container status:
```bash
docker-compose ps
```

### View application logs:
```bash
docker-compose logs whatsapp-bot
```

### Test webhook locally:
```bash
curl -X GET "https://sasthra.in/webhook?hub.verify_token=sasthra_whatsapp_verify_token_2025&hub.challenge=test"
```

### Restart services:
```bash
docker-compose restart
sudo systemctl reload nginx
```

## Support

For issues and questions, please check the logs and ensure all environment variables are properly configured.

## License

Private project for sasthra.in
